"""
OCR处理器
负责文字检测与识别
"""
import re
import os
import json
import cv2
import numpy as np
from typing import Optional
from paddleocr import PaddleOCR
from PIL import Image, ImageDraw, ImageFont
from models.data_models import OCRResult, TextRegion, ProcessingResult
from config.settings import get_config_manager


class OCRProcessor:
    """OCR处理器"""
    
    def __init__(self):
        """初始化OCR处理器"""
        self._ocr_instance: Optional[PaddleOCR] = None
        self._chinese_pattern = re.compile(r'[\u4e00-\u9fff]+')
        self.config_manager = get_config_manager()
    
    def _get_ocr_instance(self) -> Optional[PaddleOCR]:
        """获取OCR实例，避免重复初始化"""
        if self._ocr_instance is None:
            print("初始化PaddleOCR...")
            try:
                self._ocr_instance = PaddleOCR(
                    use_doc_orientation_classify=False,
                    use_doc_unwarping=False,
                    use_textline_orientation=False
                )
                print("PaddleOCR初始化完成")
            except Exception as e:
                print(f"PaddleOCR初始化失败: {e}")
                return None
        return self._ocr_instance
    
    def is_chinese_text(self, text: str) -> bool:
        """判断文本是否包含中文字符"""
        return bool(self._chinese_pattern.search(text))
    
    def process_image(self, image_path: str, confidence_threshold: float = 0.5) -> ProcessingResult:
        """
        处理图像进行OCR识别，包含样式分析

        Args:
            image_path: 图像文件路径
            confidence_threshold: 置信度阈值

        Returns:
            ProcessingResult: 包含OCRResult的处理结果
        """
        try:
            # 检查文件是否存在
            if not os.path.exists(image_path):
                return ProcessingResult.error_result(f"图像文件不存在: {image_path}")

            # 获取OCR实例
            ocr = self._get_ocr_instance()
            if ocr is None:
                return ProcessingResult.error_result("OCR初始化失败")

            print(f"处理图像: {image_path}")

            # 加载图像用于样式分析
            image = cv2.imread(image_path)
            if image is None:
                return ProcessingResult.error_result(f"无法加载图像: {image_path}")

            # OCR处理
            result = ocr.predict(input=image_path)
            if not result:
                return ProcessingResult.error_result("未检测到文字")

            # 处理OCR结果并进行样式分析
            ocr_result = self._parse_ocr_result_with_style(result, image, confidence_threshold)

            print(f"OCR处理完成: 检测到 {ocr_result.total_regions} 个文字区域")
            print(f"  中文区域: {ocr_result.chinese_count} 个")
            print(f"  其他语言区域: {len(ocr_result.other_regions)} 个")

            # 生成调试图像和数据（如果启用）
            if self.config_manager.config.enable_ocr_debug:
                self._generate_debug_outputs(image_path, ocr_result, confidence_threshold)

            return ProcessingResult.success_result(ocr_result)

        except Exception as e:
            error_msg = f"OCR处理失败: {str(e)}"
            print(error_msg)
            return ProcessingResult.error_result(error_msg)

    def _parse_ocr_result_with_style(self, raw_result, image: np.ndarray, confidence_threshold: float) -> OCRResult:
        """解析OCR结果并进行样式分析"""
        chinese_regions = []
        other_regions = []

        # 提取所有检测结果
        all_dt_polys = []
        all_rec_texts = []
        all_rec_scores = []

        for res in raw_result:
            if 'rec_texts' in res and 'dt_polys' in res:
                dt_polys = res['dt_polys']
                rec_texts = res['rec_texts']
                rec_scores = res.get('rec_scores', [1.0] * len(rec_texts))

                all_dt_polys.extend(dt_polys)
                all_rec_texts.extend(rec_texts)
                all_rec_scores.extend(rec_scores)

        # 创建文字区域对象并进行样式分析
        for i, (poly, text, score) in enumerate(zip(all_dt_polys, all_rec_texts, all_rec_scores)):
            # 过滤低置信度结果
            if score < confidence_threshold:
                continue

            is_chinese = self.is_chinese_text(text)
            region = TextRegion.from_ocr_result(i, poly, text, score, is_chinese)

            # 为中文区域进行样式分析
            if is_chinese:
                try:
                    style_info = self.extract_complete_style(image, region.bbox)
                    region.style_info = style_info
                    print(f"  中文: '{text}' (置信度: {score:.3f}, 精确高度: {style_info['precise_height']}px)")
                except Exception as e:
                    print(f"  中文: '{text}' (置信度: {score:.3f}, 样式分析失败: {e})")

                chinese_regions.append(region)
            else:
                other_regions.append(region)
                print(f"  其他: '{text}' (置信度: {score:.3f})")

        return OCRResult(
            dt_polys=all_dt_polys,
            rec_texts=all_rec_texts,
            rec_scores=all_rec_scores,
            chinese_regions=chinese_regions,
            other_regions=other_regions
        )

    def _parse_ocr_result(self, raw_result, confidence_threshold: float) -> OCRResult:
        """解析原始OCR结果"""
        chinese_regions = []
        other_regions = []
        
        # 提取所有检测结果
        all_dt_polys = []
        all_rec_texts = []
        all_rec_scores = []
        
        for res in raw_result:
            if 'rec_texts' in res and 'dt_polys' in res:
                dt_polys = res['dt_polys']
                rec_texts = res['rec_texts']
                rec_scores = res.get('rec_scores', [1.0] * len(rec_texts))
                
                all_dt_polys.extend(dt_polys)
                all_rec_texts.extend(rec_texts)
                all_rec_scores.extend(rec_scores)
        
        # 创建文字区域对象
        for i, (poly, text, score) in enumerate(zip(all_dt_polys, all_rec_texts, all_rec_scores)):
            # 过滤低置信度结果
            if score < confidence_threshold:
                continue
                
            is_chinese = self.is_chinese_text(text)
            region = TextRegion.from_ocr_result(i, poly, text, score, is_chinese)
            
            if is_chinese:
                chinese_regions.append(region)
                print(f"  中文: '{text}' (置信度: {score:.3f})")
            else:
                other_regions.append(region)
                print(f"  其他: '{text}' (置信度: {score:.3f})")
        
        return OCRResult(
            dt_polys=all_dt_polys,
            rec_texts=all_rec_texts,
            rec_scores=all_rec_scores,
            chinese_regions=chinese_regions,
            other_regions=other_regions
        )
    
    def _generate_debug_outputs(self, image_path: str, ocr_result: OCRResult, confidence_threshold: float):
        """生成调试图像和数据文件"""
        try:
            # 确保调试目录存在
            debug_dir = self.config_manager.config.ocr_debug_dir
            os.makedirs(debug_dir, exist_ok=True)
            
            # 生成调试图像
            self._save_chinese_regions_debug(image_path, ocr_result, debug_dir)
            self._save_final_regions_debug(image_path, ocr_result, debug_dir)
            
            # 保存JSON数据
            self._save_ocr_data_json(ocr_result, confidence_threshold, debug_dir)
            
            print(f"OCR调试文件已保存到: {debug_dir}")
            
        except Exception as e:
            print(f"生成OCR调试文件失败: {e}")
    
    def _save_chinese_regions_debug(self, image_path: str, ocr_result: OCRResult, debug_dir: str):
        """保存中文区域标注图像"""
        image = cv2.imread(image_path)
        if image is None:
            return
        
        # 转换为PIL图像以支持中文显示
        pil_image = Image.fromarray(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
        draw = ImageDraw.Draw(pil_image)
        
        # 尝试加载中文字体
        try:
            # 尝试使用系统中文字体
            font = ImageFont.truetype("/System/Library/Fonts/PingFang.ttc", 20)
        except:
            try:
                # 尝试使用项目中的字体
                font_path = os.path.join(self.config_manager.get_fonts_dir(), "思源黑体", "SourceHanSans-VF.otf")
                font = ImageFont.truetype(font_path, 20)
            except:
                # 使用默认字体
                font = ImageFont.load_default()
        
        # 绘制中文区域（蓝色）
        for region in ocr_result.chinese_regions:
            x, y, w, h = region.bbox
            # 绘制矩形框
            draw.rectangle([x, y, x + w, y + h], outline=(0, 0, 255), width=2)  # 蓝色
            
            # 添加文字标签
            label = f"{region.text}"
            draw.text((x, y - 25), label, fill=(0, 0, 255), font=font)
        
        # 绘制非中文区域（红色）
        for region in ocr_result.other_regions:
            x, y, w, h = region.bbox
            # 绘制矩形框
            draw.rectangle([x, y, x + w, y + h], outline=(255, 0, 0), width=2)  # 红色
            
            # 添加文字标签
            label = f"{region.text}"
            draw.text((x, y - 25), label, fill=(255, 0, 0), font=font)
        
        # 转换回OpenCV格式并保存
        final_image = cv2.cvtColor(np.array(pil_image), cv2.COLOR_RGB2BGR)
        output_path = os.path.join(debug_dir, "chinese_regions.png")
        cv2.imwrite(output_path, final_image)
    
    def _save_final_regions_debug(self, image_path: str, ocr_result: OCRResult, debug_dir: str):
        """保存最终处理区域图像"""
        image = cv2.imread(image_path)
        if image is None:
            return
        
        # 转换为PIL图像以支持中文显示
        pil_image = Image.fromarray(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
        draw = ImageDraw.Draw(pil_image)
        
        # 尝试加载中文字体
        try:
            # 尝试使用系统中文字体
            font = ImageFont.truetype("/System/Library/Fonts/PingFang.ttc", 20)
        except:
            try:
                # 尝试使用项目中的字体
                font_path = os.path.join(self.config_manager.get_fonts_dir(), "思源黑体", "SourceHanSans-VF.otf")
                font = ImageFont.truetype(font_path, 20)
            except:
                # 使用默认字体
                font = ImageFont.load_default()
        
        # 绘制最终的中文区域（绿色）
        for region in ocr_result.chinese_regions:
            x, y, w, h = region.bbox
            # 绘制矩形框
            draw.rectangle([x, y, x + w, y + h], outline=(0, 255, 0), width=2)  # 绿色
            
            # 添加区域ID和置信度
            label = f"ID:{region.id} {region.text} ({region.score:.2f})"
            draw.text((x, y - 25), label, fill=(0, 255, 0), font=font)
        
        # 转换回OpenCV格式并保存
        final_image = cv2.cvtColor(np.array(pil_image), cv2.COLOR_RGB2BGR)
        output_path = os.path.join(debug_dir, "final_regions.png")
        cv2.imwrite(output_path, final_image)
    
    def _save_ocr_data_json(self, ocr_result: OCRResult, confidence_threshold: float, debug_dir: str):
        """保存OCR数据为JSON格式"""
        
        # 序列化OCR结果
        ocr_data = {
            "metadata": {
                "timestamp": self._get_timestamp(),
                "confidence_threshold": confidence_threshold,
                "total_regions": ocr_result.total_regions,
                "chinese_count": ocr_result.chinese_count,
                "other_regions_count": len(ocr_result.other_regions)
            },
            "chinese_regions": [
                {
                    "id": region.id,                    # 区域唯一标识符
                    "text": region.text,                # 识别的文字内容
                    "bbox": list(region.bbox),          # 边界框 [x, y, width, height]
                    "poly": region.poly.tolist(),       # 多边形顶点坐标 [[x1,y1], [x2,y2], ...]
                    "score": region.score,              # OCR置信度分数 (0.0-1.0)
                    "is_chinese": region.is_chinese,    # 是否为中文文本
                    "center": list(region.center)       # 区域中心点坐标 [x, y]
                }
                for region in ocr_result.chinese_regions
            ],
            "other_regions": [
                {
                    "id": region.id,                    # 区域唯一标识符
                    "text": region.text,                # 识别的文字内容
                    "bbox": list(region.bbox),          # 边界框 [x, y, width, height]
                    "poly": region.poly.tolist(),       # 多边形顶点坐标 [[x1,y1], [x2,y2], ...]
                    "score": region.score,              # OCR置信度分数 (0.0-1.0)
                    "is_chinese": region.is_chinese,    # 是否为中文文本
                    "center": list(region.center)       # 区域中心点坐标 [x, y]
                }
                for region in ocr_result.other_regions
            ],
            "raw_data": {
                "dt_polys_count": len(ocr_result.dt_polys),     # 原始检测多边形数量
                "rec_texts": ocr_result.rec_texts,              # 原始识别文本列表
                "rec_scores": ocr_result.rec_scores             # 原始置信度分数列表
            }
        }
        
        # 保存JSON文件
        output_path = os.path.join(debug_dir, "ocr_data.json")
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(ocr_data, f, ensure_ascii=False, indent=2)
    
    def measure_roi_text_height(self, roi_bgr: np.ndarray, debug_prefix: Optional[str] = None) -> int:
        """
        对OCR裁剪区域测真实笔画高度，并在debug目录中保存debug图像

        Args:
            roi_bgr: 裁剪的BGR图像
            debug_prefix: 保存文件名前缀（可选）

        Returns:
            int: 文字实际高度 (像素)
        """
        try:
            # 确保调试目录存在
            debug_dir = os.path.join(self.config_manager.config.ocr_debug_dir, "height_measurement")
            os.makedirs(debug_dir, exist_ok=True)

            # 转换为灰度图
            gray = cv2.cvtColor(roi_bgr, cv2.COLOR_BGR2GRAY)

            # 1) 轻度高斯模糊，降低噪点影响
            blur = cv2.GaussianBlur(gray, (3, 3), 0)

            # 2) Otsu 自适应阈值，自动找最佳分割点
            _, binary = cv2.threshold(blur, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)

            # 3) 闭运算 (膨胀→腐蚀) 填补细小空洞，但不削弱笔画尖端
            kernel = np.ones((3, 3), np.uint8)
            binary_clean = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel, iterations=1)

            # 4) 查找轮廓
            contours, _ = cv2.findContours(binary_clean, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            if not contours:
                # 没有找到文字像素，返回ROI高度
                return roi_bgr.shape[0]

            # 5) 合并所有轮廓点，得到整体文字外框
            all_pts = np.vstack(contours)
            x, y, w, h = cv2.boundingRect(all_pts)

            # 6) 生成调试图像（如果启用调试且提供了前缀）
            if debug_prefix and self.config_manager.config.enable_ocr_debug:
                self._save_height_measurement_debug(
                    roi_bgr, binary_clean, (x, y, w, h), debug_prefix, debug_dir
                )

            return h

        except Exception as e:
            print(f"高度测量失败: {e}")
            # 返回ROI高度作为兜底
            return roi_bgr.shape[0]

    def _save_height_measurement_debug(
        self,
        roi_bgr: np.ndarray,
        binary_clean: np.ndarray,
        bbox: tuple,
        prefix: str,
        debug_dir: str
    ):
        """保存高度测量调试图像"""
        try:
            safe_prefix = prefix.replace("/", "_").replace(" ", "_")

            # 保存原始ROI
            cv2.imwrite(os.path.join(debug_dir, f"{safe_prefix}_roi.png"), roi_bgr)

            # 保存二值化结果
            cv2.imwrite(os.path.join(debug_dir, f"{safe_prefix}_binary.png"), binary_clean)

            # 保存标注边界框的图像
            x, y, w, h = bbox
            bbox_img = roi_bgr.copy()
            cv2.rectangle(bbox_img, (x, y), (x + w, y + h), (0, 0, 255), 2)

            # 添加高度标注
            cv2.putText(bbox_img, f"H:{h}px", (x, y - 10),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 255), 1)

            cv2.imwrite(os.path.join(debug_dir, f"{safe_prefix}_bbox.png"), bbox_img)

        except Exception as e:
            print(f"保存高度测量调试图像失败: {e}")

    def extract_colors_advanced(self, text_region: np.ndarray) -> tuple:
        """
        改进的颜色提取算法，使用直方图双峰检测

        Args:
            text_region: 文字区域图像

        Returns:
            tuple: (text_color, bg_color, is_dark_text)
        """
        try:
            if len(text_region.shape) == 3:
                # 彩色图像处理 - 使用直方图双峰检测
                return self._extract_colors_histogram(text_region)
            else:
                # 灰度图像处理
                return self._extract_colors_fallback(text_region)

        except Exception as e:
            print(f"颜色提取失败: {e}")
            # 返回默认颜色
            return (0, 0, 0), (255, 255, 255), True

    def _extract_colors_histogram(self, text_region: np.ndarray) -> tuple:
        """基于直方图双峰检测的颜色提取"""
        try:
            # 转换为灰度图进行亮度分析
            gray = cv2.cvtColor(text_region, cv2.COLOR_BGR2GRAY)

            # 计算灰度直方图
            hist = cv2.calcHist([gray], [0], None, [256], [0, 256])
            hist = hist.flatten()

            # 平滑直方图（简单移动平均替代高斯滤波）
            window_size = 5
            smoothed_hist = np.convolve(hist, np.ones(window_size)/window_size, mode='same')

            # 找到峰值
            peaks = []
            for i in range(1, len(smoothed_hist) - 1):
                if (smoothed_hist[i] > smoothed_hist[i-1] and
                    smoothed_hist[i] > smoothed_hist[i+1] and
                    smoothed_hist[i] > np.max(smoothed_hist) * 0.1):  # 至少是最大值的10%
                    peaks.append((i, smoothed_hist[i]))

            # 按峰值高度排序，取前两个
            peaks.sort(key=lambda x: x[1], reverse=True)

            if len(peaks) >= 2:
                # 有两个明显的峰值，假设较暗的是文字，较亮的是背景
                peak1_pos, peak2_pos = peaks[0][0], peaks[1][0]
                dark_peak = min(peak1_pos, peak2_pos)
                light_peak = max(peak1_pos, peak2_pos)

                # 创建掩码分离文字和背景像素
                text_mask = gray <= (dark_peak + light_peak) // 2
                bg_mask = ~text_mask

            else:
                # 只有一个峰值或没有明显峰值，使用OTSU阈值
                _, binary = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
                text_mask = binary == 0  # 假设黑色是文字
                bg_mask = binary == 255

            # 提取真实颜色
            text_pixels = text_region[text_mask]
            bg_pixels = text_region[bg_mask]

            if len(text_pixels) > 0:
                text_color = tuple(map(int, np.mean(text_pixels, axis=0)))
            else:
                text_color = (0, 0, 0)

            if len(bg_pixels) > 0:
                bg_color = tuple(map(int, np.mean(bg_pixels, axis=0)))
            else:
                bg_color = (255, 255, 255)

            # 判断是否为深色文字
            text_brightness = np.mean(text_color)
            bg_brightness = np.mean(bg_color)
            is_dark_text = text_brightness < bg_brightness

            return text_color, bg_color, is_dark_text

        except Exception as e:
            print(f"直方图颜色提取失败: {e}")
            return self._extract_colors_fallback(text_region)

    def _extract_colors_fallback(self, text_region: np.ndarray) -> tuple:
        """备选颜色提取方法"""
        try:
            if len(text_region.shape) == 3:
                gray = cv2.cvtColor(text_region, cv2.COLOR_BGR2GRAY)
            else:
                gray = text_region

            # 使用OTSU二值化
            _, binary = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)

            black_pixels = np.sum(binary == 0)
            white_pixels = np.sum(binary == 255)

            if black_pixels < white_pixels:
                text_mask = (binary == 0)
                is_dark_text = True
            else:
                text_mask = (binary == 255)
                is_dark_text = False

            if len(text_region.shape) == 3:
                # 提取真实颜色
                text_pixels = text_region[text_mask]
                bg_pixels = text_region[~text_mask]

                if len(text_pixels) > 0:
                    text_color = tuple(map(int, np.mean(text_pixels, axis=0)))
                else:
                    text_color = (0, 0, 0) if is_dark_text else (255, 255, 255)

                if len(bg_pixels) > 0:
                    bg_color = tuple(map(int, np.mean(bg_pixels, axis=0)))
                else:
                    bg_color = (255, 255, 255) if is_dark_text else (0, 0, 0)
            else:
                # 灰度图
                text_color = (0, 0, 0) if is_dark_text else (255, 255, 255)
                bg_color = (255, 255, 255) if is_dark_text else (0, 0, 0)

            return text_color, bg_color, is_dark_text

        except Exception as e:
            print(f"备选颜色提取失败: {e}")
            return (0, 0, 0), (255, 255, 255), True

    def extract_complete_style(self, image: np.ndarray, region_bbox: tuple) -> dict:
        """
        完整的样式信息提取

        Args:
            image: 原始图像
            region_bbox: 区域边界框 (x, y, w, h)

        Returns:
            dict: 完整的样式信息
        """
        try:
            x, y, w, h = region_bbox

            # 确保区域在图像范围内
            x = max(0, x)
            y = max(0, y)
            w = min(w, image.shape[1] - x)
            h = min(h, image.shape[0] - y)

            if w <= 0 or h <= 0:
                return self._default_style_info()

            # 提取文字区域
            text_region = image[y:y+h, x:x+w]

            # 1. 精确高度测量
            precise_height = self.measure_roi_text_height(text_region, f"style_extract_{x}_{y}")

            # 2. 高级颜色识别
            text_color, bg_color, is_dark_text = self.extract_colors_advanced(text_region)

            # 3. 估算字体大小（基于精确高度）
            estimated_font_size = max(16, int(precise_height * 0.95))

            # 4. 粗体检测
            is_bold = self._detect_bold_text(text_region)

            # 5. 计算对比度
            contrast_ratio = self._calculate_contrast_ratio(text_color, bg_color)

            return {
                'estimated_font_size': estimated_font_size,
                'precise_height': precise_height,  # 新增：精确高度
                'text_color': text_color,
                'background_color': bg_color,
                'is_bold': is_bold,
                'is_dark_text': is_dark_text,
                'contrast_ratio': contrast_ratio,
                'region_width': w,
                'region_height': h
            }

        except Exception as e:
            print(f"样式提取失败: {e}")
            return self._default_style_info()

    def _detect_bold_text(self, text_region: np.ndarray) -> bool:
        """检测是否为粗体文字"""
        try:
            # 转换为灰度图
            if len(text_region.shape) == 3:
                gray = cv2.cvtColor(text_region, cv2.COLOR_BGR2GRAY)
            else:
                gray = text_region.copy()

            # 二值化
            _, binary = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)

            # 计算笔画密度
            text_pixels = np.sum(binary == 0)  # 黑色文字像素
            total_pixels = gray.shape[0] * gray.shape[1]
            fill_ratio = text_pixels / total_pixels

            # 形态学操作分析笔画厚度
            kernel = np.ones((2, 2), np.uint8)
            eroded = cv2.erode(binary, kernel, iterations=1)
            eroded_pixels = np.sum(eroded == 0)
            thickness_retention = eroded_pixels / max(text_pixels, 1)

            # 粗体判断：高填充率 + 高厚度保持率
            is_bold = (fill_ratio > 0.25 and thickness_retention > 0.6) or fill_ratio > 0.4

            return is_bold

        except Exception as e:
            print(f"粗体检测失败: {e}")
            return False

    def _calculate_contrast_ratio(self, color1: tuple, color2: tuple) -> float:
        """计算两个颜色之间的对比度"""
        try:
            # 计算相对亮度
            def relative_luminance(rgb):
                r, g, b = [c / 255.0 for c in rgb]
                # 应用gamma校正
                def gamma_correct(c):
                    return c / 12.92 if c <= 0.03928 else ((c + 0.055) / 1.055) ** 2.4

                r, g, b = map(gamma_correct, [r, g, b])
                return 0.2126 * r + 0.7152 * g + 0.0722 * b

            lum1 = relative_luminance(color1)
            lum2 = relative_luminance(color2)

            # 确保较亮的颜色在分子
            lighter = max(lum1, lum2)
            darker = min(lum1, lum2)

            # 计算对比度比率
            contrast_ratio = (lighter + 0.05) / (darker + 0.05)
            return contrast_ratio

        except Exception as e:
            print(f"对比度计算失败: {e}")
            return 1.0

    def _default_style_info(self) -> dict:
        """返回默认样式信息"""
        return {
            'estimated_font_size': 24,
            'precise_height': 24,
            'text_color': (0, 0, 0),
            'background_color': (255, 255, 255),
            'is_bold': False,
            'is_dark_text': True,
            'contrast_ratio': 21.0,
            'region_width': 0,
            'region_height': 0
        }

    def _get_timestamp(self) -> str:
        """获取当前时间戳"""
        from datetime import datetime
        return datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    def cleanup(self):
        """清理资源"""
        if self._ocr_instance is not None:
            # PaddleOCR没有显式的清理方法，设置为None让GC处理
            self._ocr_instance = None
            print("OCR实例已清理")
