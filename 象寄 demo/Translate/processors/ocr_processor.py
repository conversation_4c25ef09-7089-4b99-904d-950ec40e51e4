"""
OCR处理器
负责文字检测与识别
"""
import re
import os
import json
import cv2
import numpy as np
from typing import Optional
from paddleocr import PaddleOCR
from PIL import Image, ImageDraw, ImageFont
from models.data_models import OCRResult, TextRegion, ProcessingResult
from config.settings import get_config_manager


class OCRProcessor:
    """OCR处理器"""
    
    def __init__(self):
        """初始化OCR处理器"""
        self._ocr_instance: Optional[PaddleOCR] = None
        self._chinese_pattern = re.compile(r'[\u4e00-\u9fff]+')
        self.config_manager = get_config_manager()
    
    def _get_ocr_instance(self) -> Optional[PaddleOCR]:
        """获取OCR实例，避免重复初始化"""
        if self._ocr_instance is None:
            print("初始化PaddleOCR...")
            try:
                self._ocr_instance = PaddleOCR(
                    use_doc_orientation_classify=False,
                    use_doc_unwarping=False,
                    use_textline_orientation=False
                )
                print("PaddleOCR初始化完成")
            except Exception as e:
                print(f"PaddleOCR初始化失败: {e}")
                return None
        return self._ocr_instance
    
    def is_chinese_text(self, text: str) -> bool:
        """判断文本是否包含中文字符"""
        return bool(self._chinese_pattern.search(text))
    
    def process_image(self, image_path: str, confidence_threshold: float = 0.5) -> ProcessingResult:
        """
        处理图像进行OCR识别
        
        Args:
            image_path: 图像文件路径
            confidence_threshold: 置信度阈值
            
        Returns:
            ProcessingResult: 包含OCRResult的处理结果
        """
        try:
            # 检查文件是否存在
            if not os.path.exists(image_path):
                return ProcessingResult.error_result(f"图像文件不存在: {image_path}")

            # 获取OCR实例
            ocr = self._get_ocr_instance()
            if ocr is None:
                return ProcessingResult.error_result("OCR初始化失败")

            print(f"处理图像: {image_path}")

            # OCR处理
            result = ocr.predict(input=image_path)
            if not result:
                return ProcessingResult.error_result("未检测到文字")
            
            # 处理OCR结果
            ocr_result = self._parse_ocr_result(result, confidence_threshold)
            
            print(f"OCR处理完成: 检测到 {ocr_result.total_regions} 个文字区域")
            print(f"  中文区域: {ocr_result.chinese_count} 个")
            print(f"  其他语言区域: {len(ocr_result.other_regions)} 个")
            
            # 生成调试图像和数据（如果启用）
            if self.config_manager.config.enable_ocr_debug:
                self._generate_debug_outputs(image_path, ocr_result, confidence_threshold)
            
            return ProcessingResult.success_result(ocr_result)
            
        except Exception as e:
            error_msg = f"OCR处理失败: {str(e)}"
            print(error_msg)
            return ProcessingResult.error_result(error_msg)
    
    def _parse_ocr_result(self, raw_result, confidence_threshold: float) -> OCRResult:
        """解析原始OCR结果"""
        chinese_regions = []
        other_regions = []
        
        # 提取所有检测结果
        all_dt_polys = []
        all_rec_texts = []
        all_rec_scores = []
        
        for res in raw_result:
            if 'rec_texts' in res and 'dt_polys' in res:
                dt_polys = res['dt_polys']
                rec_texts = res['rec_texts']
                rec_scores = res.get('rec_scores', [1.0] * len(rec_texts))
                
                all_dt_polys.extend(dt_polys)
                all_rec_texts.extend(rec_texts)
                all_rec_scores.extend(rec_scores)
        
        # 创建文字区域对象
        for i, (poly, text, score) in enumerate(zip(all_dt_polys, all_rec_texts, all_rec_scores)):
            # 过滤低置信度结果
            if score < confidence_threshold:
                continue
                
            is_chinese = self.is_chinese_text(text)
            region = TextRegion.from_ocr_result(i, poly, text, score, is_chinese)
            
            if is_chinese:
                chinese_regions.append(region)
                print(f"  中文: '{text}' (置信度: {score:.3f})")
            else:
                other_regions.append(region)
                print(f"  其他: '{text}' (置信度: {score:.3f})")
        
        return OCRResult(
            dt_polys=all_dt_polys,
            rec_texts=all_rec_texts,
            rec_scores=all_rec_scores,
            chinese_regions=chinese_regions,
            other_regions=other_regions
        )
    
    def _generate_debug_outputs(self, image_path: str, ocr_result: OCRResult, confidence_threshold: float):
        """生成调试图像和数据文件"""
        try:
            # 确保调试目录存在
            debug_dir = self.config_manager.config.ocr_debug_dir
            os.makedirs(debug_dir, exist_ok=True)
            
            # 生成调试图像
            self._save_chinese_regions_debug(image_path, ocr_result, debug_dir)
            self._save_final_regions_debug(image_path, ocr_result, debug_dir)
            
            # 保存JSON数据
            self._save_ocr_data_json(ocr_result, confidence_threshold, debug_dir)
            
            print(f"OCR调试文件已保存到: {debug_dir}")
            
        except Exception as e:
            print(f"生成OCR调试文件失败: {e}")
    
    def _save_chinese_regions_debug(self, image_path: str, ocr_result: OCRResult, debug_dir: str):
        """保存中文区域标注图像"""
        image = cv2.imread(image_path)
        if image is None:
            return
        
        # 转换为PIL图像以支持中文显示
        pil_image = Image.fromarray(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
        draw = ImageDraw.Draw(pil_image)
        
        # 尝试加载中文字体
        try:
            # 尝试使用系统中文字体
            font = ImageFont.truetype("/System/Library/Fonts/PingFang.ttc", 20)
        except:
            try:
                # 尝试使用项目中的字体
                font_path = os.path.join(self.config_manager.get_fonts_dir(), "思源黑体", "SourceHanSans-VF.otf")
                font = ImageFont.truetype(font_path, 20)
            except:
                # 使用默认字体
                font = ImageFont.load_default()
        
        # 绘制中文区域（蓝色）
        for region in ocr_result.chinese_regions:
            x, y, w, h = region.bbox
            # 绘制矩形框
            draw.rectangle([x, y, x + w, y + h], outline=(0, 0, 255), width=2)  # 蓝色
            
            # 添加文字标签
            label = f"{region.text}"
            draw.text((x, y - 25), label, fill=(0, 0, 255), font=font)
        
        # 绘制非中文区域（红色）
        for region in ocr_result.other_regions:
            x, y, w, h = region.bbox
            # 绘制矩形框
            draw.rectangle([x, y, x + w, y + h], outline=(255, 0, 0), width=2)  # 红色
            
            # 添加文字标签
            label = f"{region.text}"
            draw.text((x, y - 25), label, fill=(255, 0, 0), font=font)
        
        # 转换回OpenCV格式并保存
        final_image = cv2.cvtColor(np.array(pil_image), cv2.COLOR_RGB2BGR)
        output_path = os.path.join(debug_dir, "chinese_regions.png")
        cv2.imwrite(output_path, final_image)
    
    def _save_final_regions_debug(self, image_path: str, ocr_result: OCRResult, debug_dir: str):
        """保存最终处理区域图像"""
        image = cv2.imread(image_path)
        if image is None:
            return
        
        # 转换为PIL图像以支持中文显示
        pil_image = Image.fromarray(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
        draw = ImageDraw.Draw(pil_image)
        
        # 尝试加载中文字体
        try:
            # 尝试使用系统中文字体
            font = ImageFont.truetype("/System/Library/Fonts/PingFang.ttc", 20)
        except:
            try:
                # 尝试使用项目中的字体
                font_path = os.path.join(self.config_manager.get_fonts_dir(), "思源黑体", "SourceHanSans-VF.otf")
                font = ImageFont.truetype(font_path, 20)
            except:
                # 使用默认字体
                font = ImageFont.load_default()
        
        # 绘制最终的中文区域（绿色）
        for region in ocr_result.chinese_regions:
            x, y, w, h = region.bbox
            # 绘制矩形框
            draw.rectangle([x, y, x + w, y + h], outline=(0, 255, 0), width=2)  # 绿色
            
            # 添加区域ID和置信度
            label = f"ID:{region.id} {region.text} ({region.score:.2f})"
            draw.text((x, y - 25), label, fill=(0, 255, 0), font=font)
        
        # 转换回OpenCV格式并保存
        final_image = cv2.cvtColor(np.array(pil_image), cv2.COLOR_RGB2BGR)
        output_path = os.path.join(debug_dir, "final_regions.png")
        cv2.imwrite(output_path, final_image)
    
    def _save_ocr_data_json(self, ocr_result: OCRResult, confidence_threshold: float, debug_dir: str):
        """保存OCR数据为JSON格式"""
        
        # 序列化OCR结果
        ocr_data = {
            "metadata": {
                "timestamp": self._get_timestamp(),
                "confidence_threshold": confidence_threshold,
                "total_regions": ocr_result.total_regions,
                "chinese_count": ocr_result.chinese_count,
                "other_regions_count": len(ocr_result.other_regions)
            },
            "chinese_regions": [
                {
                    "id": region.id,                    # 区域唯一标识符
                    "text": region.text,                # 识别的文字内容
                    "bbox": list(region.bbox),          # 边界框 [x, y, width, height]
                    "poly": region.poly.tolist(),       # 多边形顶点坐标 [[x1,y1], [x2,y2], ...]
                    "score": region.score,              # OCR置信度分数 (0.0-1.0)
                    "is_chinese": region.is_chinese,    # 是否为中文文本
                    "center": list(region.center)       # 区域中心点坐标 [x, y]
                }
                for region in ocr_result.chinese_regions
            ],
            "other_regions": [
                {
                    "id": region.id,                    # 区域唯一标识符
                    "text": region.text,                # 识别的文字内容
                    "bbox": list(region.bbox),          # 边界框 [x, y, width, height]
                    "poly": region.poly.tolist(),       # 多边形顶点坐标 [[x1,y1], [x2,y2], ...]
                    "score": region.score,              # OCR置信度分数 (0.0-1.0)
                    "is_chinese": region.is_chinese,    # 是否为中文文本
                    "center": list(region.center)       # 区域中心点坐标 [x, y]
                }
                for region in ocr_result.other_regions
            ],
            "raw_data": {
                "dt_polys_count": len(ocr_result.dt_polys),     # 原始检测多边形数量
                "rec_texts": ocr_result.rec_texts,              # 原始识别文本列表
                "rec_scores": ocr_result.rec_scores             # 原始置信度分数列表
            }
        }
        
        # 保存JSON文件
        output_path = os.path.join(debug_dir, "ocr_data.json")
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(ocr_data, f, ensure_ascii=False, indent=2)
    
    def _get_timestamp(self) -> str:
        """获取当前时间戳"""
        from datetime import datetime
        return datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    def cleanup(self):
        """清理资源"""
        if self._ocr_instance is not None:
            # PaddleOCR没有显式的清理方法，设置为None让GC处理
            self._ocr_instance = None
            print("OCR实例已清理")
