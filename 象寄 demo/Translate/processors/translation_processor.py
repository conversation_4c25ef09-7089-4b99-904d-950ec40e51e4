"""
翻译处理器
负责文本翻译与样式提取
"""
import cv2
import numpy as np
from typing import List, Tuple, Dict, Any
from PIL import Image, ImageDraw, ImageFont

from models.data_models import (
    TextRegion, FontMatchResult, LayoutResult, 
    TranslationResult, StyleInfo, ProcessingResult
)
from config.settings import get_config_manager


class TranslationProcessor:
    """翻译处理器"""
    
    def __init__(self, weight_adjustment: int = 400):
        """
        初始化翻译处理器
        
        Args:
            weight_adjustment: 可变字体粗细调节值 (100-900)
        """
        self.config_manager = get_config_manager()
        self.translation_dict = self.config_manager.translation_dict
        self.font_mapping = self.config_manager.font_mapping
        self.weight_adjustment = weight_adjustment
        
        print(f"翻译处理器初始化完成，加载了 {len(self.translation_dict)} 个翻译映射")
    
    def process_translation(
        self, 
        image: np.ndarray,
        regions: List[TextRegion], 
        font_results: List[FontMatchResult],
        layout_result: LayoutResult
    ) -> ProcessingResult:
        """
        处理翻译任务
        
        Args:
            image: 原始图像
            regions: 文字区域列表
            font_results: 字体匹配结果
            layout_result: 布局分析结果
            
        Returns:
            ProcessingResult: 包含TranslationResult列表的处理结果
        """
        try:
            translation_results = []
            
            # 创建字体结果映射
            font_map = {fr.region_id: fr for fr in font_results}
            
            for region in regions:
                if not region.is_chinese:
                    continue
                
                # 获取对应的字体信息
                font_info = font_map.get(region.id)
                if not font_info:
                    continue
                
                # 翻译文本
                translated_text = self._translate_text(region.text)
                if translated_text == region.text:
                    continue  # 跳过未翻译的文本
                
                # 提取样式信息
                style_info = self._extract_text_style(image, region.poly)
                
                # 创建翻译结果
                translation_result = TranslationResult(
                    original_text=region.text,
                    translated_text=translated_text,
                    bbox=region.bbox,
                    style_info=style_info,
                    font_info=font_info,
                    group_key="",  # 将在布局感知处理中设置
                    group_scale_factor=1.0
                )
                
                translation_results.append(translation_result)
                
                print(f"翻译: '{region.text}' → '{translated_text}'")
            
            # 应用布局感知分组策略
            processed_results = self._apply_layout_aware_strategy(
                translation_results, image, layout_result
            )
            
            print(f"翻译处理完成，处理了 {len(processed_results)} 个翻译结果")
            return ProcessingResult.success_result(processed_results)
            
        except Exception as e:
            error_msg = f"翻译处理失败: {str(e)}"
            print(error_msg)
            return ProcessingResult.error_result(error_msg)
    
    def _translate_text(self, text: str) -> str:
        """翻译文本"""
        # 直接查找翻译
        if text in self.translation_dict:
            return self.translation_dict[text]
        
        # 去除引号后查找
        cleaned_text = text.strip('"\'""''')
        if cleaned_text in self.translation_dict:
            return self.translation_dict[cleaned_text]
        
        # 未找到翻译，返回原文
        return text
    
    def _extract_text_style(self, image: np.ndarray, poly: np.ndarray) -> StyleInfo:
        """提取文字样式信息"""
        try:
            # 获取文字区域
            points = np.array(poly, dtype=np.int32)
            x, y, w, h = cv2.boundingRect(points)
            
            # 确保区域在图像范围内
            x = max(0, x)
            y = max(0, y)
            w = min(w, image.shape[1] - x)
            h = min(h, image.shape[0] - y)
            
            if w <= 0 or h <= 0:
                return self._default_style_info()
            
            region = image[y:y+h, x:x+w]
            
            # 转换为灰度图
            if len(region.shape) == 3:
                gray = cv2.cvtColor(region, cv2.COLOR_BGR2GRAY)
            else:
                gray = region.copy()
            
            # 估算字体大小（基于区域高度）
            estimated_font_size = max(16, int(h * 0.8))
            
            # 分析颜色
            if len(region.shape) == 3:
                # 计算平均颜色
                mean_color = np.mean(region.reshape(-1, 3), axis=0)
                text_color = tuple(map(int, mean_color))
            else:
                # 灰度图，假设为黑色文字
                text_color = (0, 0, 0)
            
            # 估算背景颜色（边缘像素的平均值）
            if len(region.shape) == 3:
                edge_pixels = np.concatenate([
                    region[0, :].reshape(-1, 3),  # 上边缘
                    region[-1, :].reshape(-1, 3),  # 下边缘
                    region[:, 0].reshape(-1, 3),  # 左边缘
                    region[:, -1].reshape(-1, 3)  # 右边缘
                ])
                bg_color = tuple(map(int, np.mean(edge_pixels, axis=0)))
            else:
                bg_color = (255, 255, 255)  # 默认白色背景
            
            # 计算对比度
            contrast_ratio = self._calculate_contrast_ratio(text_color, bg_color)
            
            # 检测是否为粗体（基于边缘密度）
            edges = cv2.Canny(gray, 50, 150)
            edge_density = np.sum(edges > 0) / (gray.shape[0] * gray.shape[1])
            is_bold = edge_density > 0.1  # 阈值可调整
            
            return StyleInfo(
                estimated_font_size=estimated_font_size,
                color=text_color,
                background_color=bg_color,
                is_bold=is_bold,
                contrast_ratio=contrast_ratio
            )
            
        except Exception as e:
            print(f"样式提取失败: {e}")
            return self._default_style_info()
    
    def _default_style_info(self) -> StyleInfo:
        """返回默认样式信息"""
        return StyleInfo(
            estimated_font_size=24,
            color=(0, 0, 0),
            background_color=(255, 255, 255),
            is_bold=False,
            contrast_ratio=21.0
        )
    
    def _calculate_contrast_ratio(self, color1: Tuple[int, int, int], color2: Tuple[int, int, int]) -> float:
        """计算两个颜色的对比度"""
        def luminance(color):
            r, g, b = [c / 255.0 for c in color]
            r = r / 12.92 if r <= 0.03928 else ((r + 0.055) / 1.055) ** 2.4
            g = g / 12.92 if g <= 0.03928 else ((g + 0.055) / 1.055) ** 2.4
            b = b / 12.92 if b <= 0.03928 else ((b + 0.055) / 1.055) ** 2.4
            return 0.2126 * r + 0.7152 * g + 0.0722 * b
        
        l1 = luminance(color1)
        l2 = luminance(color2)
        
        lighter = max(l1, l2)
        darker = min(l1, l2)
        
        return (lighter + 0.05) / (darker + 0.05)
    
    def _apply_layout_aware_strategy(
        self, 
        translation_results: List[TranslationResult],
        image: np.ndarray,
        layout_result: LayoutResult
    ) -> List[TranslationResult]:
        """应用布局感知分组策略"""
        try:
            # 简化的分组策略：基于垂直位置分组
            groups = {}
            
            for result in translation_results:
                x, y, w, h = result.bbox
                
                # 基于Y坐标分组（允许一定误差）
                group_key = f"row_{y // 50}"  # 每50像素为一组
                
                if group_key not in groups:
                    groups[group_key] = []
                groups[group_key].append(result)
            
            # 为每个分组计算缩放因子
            processed_results = []
            for group_key, group_results in groups.items():
                if len(group_results) == 1:
                    # 单个文字，使用默认缩放
                    result = group_results[0]
                    result.group_key = group_key
                    result.group_scale_factor = 1.0
                    processed_results.append(result)
                else:
                    # 多个文字，计算统一缩放因子
                    scale_factor = self._calculate_group_scale_factor(group_results)
                    
                    for result in group_results:
                        result.group_key = group_key
                        result.group_scale_factor = scale_factor
                        processed_results.append(result)
            
            return processed_results
            
        except Exception as e:
            print(f"布局感知策略应用失败: {e}")
            # 返回原始结果
            for result in translation_results:
                result.group_key = "default"
                result.group_scale_factor = 1.0
            return translation_results
    
    def _calculate_group_scale_factor(self, group_results: List[TranslationResult]) -> float:
        """计算分组的缩放因子"""
        # 简化实现：基于平均字体大小
        total_font_size = sum(result.style_info.estimated_font_size for result in group_results)
        avg_font_size = total_font_size / len(group_results)
        
        # 基于平均字体大小计算缩放因子
        if avg_font_size < 20:
            return 0.9
        elif avg_font_size > 40:
            return 1.1
        else:
            return 1.0
