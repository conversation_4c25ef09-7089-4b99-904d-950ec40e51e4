"""
配置管理模块
集中管理所有配置参数和路径
"""
import os
import json
from typing import Dict, Any
from models.data_models import PipelineConfig


class ConfigManager:
    """配置管理器"""
    
    def __init__(self, base_dir: str = None):
        """
        初始化配置管理器
        
        Args:
            base_dir: 项目根目录，默认为当前文件的上级目录
        """
        if base_dir is None:
            self.base_dir = os.path.dirname(os.path.dirname(__file__))
        else:
            self.base_dir = base_dir
            
        self._config = PipelineConfig()
        self._font_mapping = {}
        self._translation_dict = {}
        
        # 初始化配置
        self._load_font_mapping()
        self._load_translation_dict()
    
    @property
    def config(self) -> PipelineConfig:
        """获取流水线配置"""
        return self._config
    
    @property
    def font_mapping(self) -> Dict[str, str]:
        """获取字体映射"""
        return self._font_mapping.copy()
    
    @property
    def translation_dict(self) -> Dict[str, str]:
        """获取翻译字典"""
        return self._translation_dict.copy()
    
    def get_fonts_dir(self) -> str:
        """获取字体目录路径"""
        return os.path.join(self.base_dir, self._config.fonts_dir)
    
    def get_output_dir(self) -> str:
        """获取输出目录路径"""
        return os.path.join(self.base_dir, self._config.output_dir)
    
    def ensure_output_dir(self) -> str:
        """确保输出目录存在"""
        output_dir = self.get_output_dir()
        os.makedirs(output_dir, exist_ok=True)
        return output_dir
    
    def _load_font_mapping(self):
        """加载字体映射配置"""
        fonts_dir = self.get_fonts_dir()
        
        # 本地字体配置
        font_configs = {
            'NotoSansSC': {
                'path': 'NotoSansSC/NotoSansSC-Black.ttf',
                'name': 'Noto Sans SC Black'
            },
            'SpoqaHanSans': {
                'path': 'SpoqaHanSans/SpoqaHanSansBold.ttf', 
                'name': 'Spoqa Han Sans Bold'
            },
            '台北黑体': {
                'path': '台北黑体/TaipeiSans-Bold.ttf',
                'name': '台北黑体 TC Bold'
            },
            '思源黑体': {
                'path': '思源黑体/SourceHanSans-VF.otf',
                'name': '思源黑体'
            }
        }
        
        # 构建字体映射
        for font_key, config in font_configs.items():
            font_path = os.path.join(fonts_dir, config['path'])
            if os.path.exists(font_path):
                self._font_mapping[config['name']] = font_path
        
        # 添加通用字体名称映射
        vf_path = os.path.join(fonts_dir, '思源黑体', 'SourceHanSans-VF.otf')
        if os.path.exists(vf_path):
            common_fonts = [
                '宋体', '黑体', '楷体', '微软雅黑', '仿宋',
                'Source Han Sans', 'SourceHanSans', 'SourceHanSansCN',
                'Source Han Sans CN', 'Noto Sans CJK SC', 'Noto Sans SC'
            ]
            for font_name in common_fonts:
                self._font_mapping[font_name] = vf_path
    
    def _load_translation_dict(self):
        """加载翻译字典"""
        dict_path = os.path.join(self.base_dir, self._config.translation_dict_path)
        
        # 默认翻译字典
        default_dict = {
            "护脊靠枕": "背骨サポートまくら",
            "呵护爱宠脊椎": "大切なペットの背骨ケア",
            "4D高回弹记忆棉": "4D高反発記憶フォーム",
            "久睡不塌": "長時間でも沈まない",
            "适用更久": "長く使える",
            '"0压感"': "ゼロ圧感",
            "0压感": "ゼロ圧感",
            "高回弹海绵": "高反発スポンジ",
            "深度分散压力": "圧力を分散",
            "不易塌陷": "へたりにくい"
        }
        
        if os.path.exists(dict_path):
            try:
                with open(dict_path, 'r', encoding='utf-8') as f:
                    file_dict = json.load(f)
                    self._translation_dict.update(file_dict)
            except Exception as e:
                print(f"加载翻译字典失败: {e}")
                self._translation_dict = default_dict
        else:
            self._translation_dict = default_dict
    
    def update_config(self, **kwargs):
        """更新配置参数"""
        for key, value in kwargs.items():
            if hasattr(self._config, key):
                setattr(self._config, key, value)
            else:
                print(f"警告: 未知配置参数 {key}")
    
    def add_font_mapping(self, font_name: str, font_path: str):
        """添加字体映射"""
        if os.path.exists(font_path):
            self._font_mapping[font_name] = font_path
        else:
            print(f"警告: 字体文件不存在 {font_path}")
    
    def add_translation(self, chinese: str, japanese: str):
        """添加翻译映射"""
        self._translation_dict[chinese] = japanese
    
    def save_translation_dict(self):
        """保存翻译字典到文件"""
        dict_path = os.path.join(self.base_dir, self._config.translation_dict_path)
        try:
            with open(dict_path, 'w', encoding='utf-8') as f:
                json.dump(self._translation_dict, f, ensure_ascii=False, indent=2)
            print(f"翻译字典已保存到: {dict_path}")
        except Exception as e:
            print(f"保存翻译字典失败: {e}")


# 全局配置实例
_config_manager = None

def get_config_manager() -> ConfigManager:
    """获取全局配置管理器实例"""
    global _config_manager
    if _config_manager is None:
        _config_manager = ConfigManager()
    return _config_manager
