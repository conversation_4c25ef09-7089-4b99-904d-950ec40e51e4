"""
图像翻译程序主入口
模块化重构版本
"""
import os
import sys
import argparse
from pipeline import TranslationPipeline


def main():
    """主函数"""
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='图像文字翻译工具')
    parser.add_argument('--image', '-i', type=str, default='example.jpg',
                       help='输入图像文件路径 (默认: example.jpg)')
    parser.add_argument('--font-weight', '-w', type=int, default=400,
                       choices=range(100, 901, 100),
                       help='字体粗细 (100-900, 默认: 400)')
    parser.add_argument('--no-debug', action='store_true',
                       help='禁用调试输出')
    parser.add_argument('--output-dir', '-o', type=str, default='output',
                       help='输出目录 (默认: output)')
    parser.add_argument('--enable-ocr-debug', action='store_true',
                       help='启用OCR调试模式，生成详细的调试图像和数据')
    parser.add_argument('--enable-layout-debug', action='store_true',
                       help='启用布局调试模式，生成布局分析的调试图像和数据')
    
    args = parser.parse_args()
    
    # 显示配置信息
    print("="*60)
    print("图像文字翻译工具 - 模块化版本")
    print("="*60)
    print(f"输入图像: {args.image}")
    print(f"字体粗细: {args.font_weight}")
    print(f"调试模式: {'关闭' if args.no_debug else '开启'}")
    print(f"OCR调试: {'开启' if args.enable_ocr_debug else '关闭'}")
    print(f"布局调试: {'开启' if args.enable_layout_debug else '关闭'}")
    print(f"输出目录: {args.output_dir}")
    print("="*60)
    
    # 检查输入文件
    if not os.path.exists(args.image):
        print(f"错误: 找不到图像文件 {args.image}")
        sys.exit(1)
    
    # 创建翻译流水线
    pipeline = None
    try:
        pipeline = TranslationPipeline(
            font_weight=args.font_weight,
            enable_debug=not args.no_debug
        )
        
        # 更新配置
        pipeline.update_config(
            output_dir=args.output_dir,
            enable_ocr_debug=args.enable_ocr_debug,
            enable_layout_debug=args.enable_layout_debug
        )
        
        # 显示统计信息
        stats = pipeline.get_translation_stats()
        print(f"\n配置信息:")
        print(f"  翻译词典: {stats['translation_dict_size']} 个映射")
        print(f"  字体映射: {stats['font_mapping_size']} 个字体")
        print(f"  字体目录: {stats['fonts_dir']}")
        print(f"  输出目录: {stats['output_dir']}")
        if args.enable_ocr_debug:
            print(f"  OCR调试目录: debug_images/ocr_processor")
        if args.enable_layout_debug:
            print(f"  布局调试目录: debug_images/layout_processor")
        
        # 处理图像
        result = pipeline.process_image(args.image)
        
        if result.success:
            data = result.data
            if 'render_log' in data:
                print(f"\n翻译完成统计:")
                for log in data['render_log']:
                    print(f"  '{log['original']}' → '{log['translated']}' "
                          f"(字体: {log['font']}, 大小: {log['size']}px)")
            else:
                print(f"\n{data.get('message', '处理完成')}")
        else:
            print(f"\n处理失败: {result.error_message}")
            sys.exit(1)
    
    except KeyboardInterrupt:
        print("\n\n用户中断操作")
        sys.exit(0)
    
    except Exception as e:
        print(f"\n程序异常: {str(e)}")
        sys.exit(1)
    
    finally:
        # 清理资源
        if pipeline:
            pipeline.cleanup()


def show_font_weight_help():
    """显示字体粗细说明"""
    print("\n字体粗细参数说明:")
    print("  100 - 超细 (Thin)")
    print("  200 - 细 (Extra Light)")
    print("  300 - 较细 (Light)")
    print("  400 - 正常 (Normal/Regular)")
    print("  500 - 中等 (Medium)")
    print("  600 - 半粗 (Semi Bold)")
    print("  700 - 粗 (Bold)")
    print("  800 - 超粗 (Extra Bold)")
    print("  900 - 黑体 (Black)")


def show_usage_examples():
    """显示使用示例"""
    print("\n使用示例:")
    print("  # 使用默认设置处理 example.jpg")
    print("  python main.py")
    print("")
    print("  # 指定图像文件和字体粗细")
    print("  python main.py --image my_image.jpg --font-weight 700")
    print("")
    print("  # 禁用调试输出")
    print("  python main.py --image my_image.jpg --no-debug")
    print("")
    print("  # 指定输出目录")
    print("  python main.py --image my_image.jpg --output-dir results")
    print("")
    print("  # 启用OCR调试模式")
    print("  python main.py --image my_image.jpg --enable-ocr-debug")
    print("")
    print("  # 启用布局调试模式")
    print("  python main.py --image my_image.jpg --enable-layout-debug")
    print("")
    print("  # 同时启用多个调试模式")
    print("  python main.py --image my_image.jpg --enable-ocr-debug --enable-layout-debug")
    print("")
    print("  # 组合使用多个选项")
    print("  python main.py --image my_image.jpg --font-weight 600 --enable-ocr-debug --output-dir results")


if __name__ == "__main__":
    # 检查是否请求帮助信息
    if len(sys.argv) > 1:
        if sys.argv[1] in ['--help-font', '-hf']:
            show_font_weight_help()
            sys.exit(0)
        elif sys.argv[1] in ['--examples', '-e']:
            show_usage_examples()
            sys.exit(0)
    
    main()
